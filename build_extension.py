#!/usr/bin/env python3
"""
Build script for Apply Scale Extension

This script creates a zip package that can be installed as a Blender extension.
"""

import os
import shutil
from pathlib import Path

def create_extension_package():
    """Create a zip package for the extension"""

    # Get the current directory
    current_dir = Path(__file__).parent

    # Extension files to include
    extension_files = [
        "blender_manifest.toml",
        "__init__.py",
        "README.md"
    ]

    # Output directory and filenames
    output_dir = current_dir / "dist"
    output_dir.mkdir(exist_ok=True)

    # Create extension folder name (same as the zip name but without extension)
    extension_folder_name = "apply_scale_extension"
    extension_folder_path = output_dir / extension_folder_name
    zip_filename = output_dir / f"{extension_folder_name}.zip"

    # Clean up existing folder if it exists
    if extension_folder_path.exists():
        shutil.rmtree(extension_folder_path)
        print(f"Cleaned up existing folder: {extension_folder_path}")

    # Create the extension folder
    extension_folder_path.mkdir(exist_ok=True)
    print(f"Created extension folder: {extension_folder_path}")

    # Copy files to the extension folder
    for file_path in extension_files:
        source_file = current_dir / file_path
        if source_file.exists():
            destination_file = extension_folder_path / file_path
            shutil.copy2(source_file, destination_file)
            print(f"Copied: {file_path}")
        else:
            print(f"Warning: {file_path} not found")

    # Create the zip file from the folder
    print(f"\nCreating zip file: {zip_filename}")
    if zip_filename.exists():
        zip_filename.unlink()
        print(f"Removed existing zip file")

    shutil.make_archive(
        str(zip_filename.with_suffix('')),  # Remove .zip as make_archive adds it
        'zip',
        str(output_dir),
        extension_folder_name
    )

    print(f"\nExtension package created: {zip_filename}")
    print(f"Size: {zip_filename.stat().st_size / 1024:.1f} KB")

    # Optionally clean up the folder after zipping (uncomment if desired)
    # shutil.rmtree(extension_folder_path)
    # print(f"Cleaned up temporary folder: {extension_folder_path}")

    return zip_filename

def validate_manifest():
    """Validate the manifest file"""
    manifest_path = Path(__file__).parent / "blender_manifest.toml"
    
    if not manifest_path.exists():
        print("Error: blender_manifest.toml not found!")
        return False
    
    try:
        import tomllib
    except ImportError:
        try:
            import tomli as tomllib
        except ImportError:
            print("Warning: Cannot validate TOML file (tomllib/tomli not available)")
            return True
    
    try:
        with open(manifest_path, 'rb') as f:
            manifest = tomllib.load(f)
        
        required_fields = ['schema_version', 'id', 'version', 'name', 'type']
        missing_fields = [field for field in required_fields if field not in manifest]
        
        if missing_fields:
            print(f"Error: Missing required fields in manifest: {missing_fields}")
            return False
        
        print("✓ Manifest validation passed")
        return True
        
    except Exception as e:
        print(f"Error validating manifest: {e}")
        return False

if __name__ == "__main__":
    print("Building Apply Scale Extension...")
    print("=" * 40)
    
    # Validate manifest
    if not validate_manifest():
        exit(1)
    
    # Create package
    package_path = create_extension_package()
    
    print("\n" + "=" * 40)
    print("Build completed successfully!")
    print(f"Package: {package_path}")
    print("\nTo install:")
    print("1. Open Blender 4.2+")
    print("2. Go to Edit > Preferences > Extensions")
    print("3. Click the dropdown next to + and select 'Install from Disk...'")
    print("4. Select the created zip file")
